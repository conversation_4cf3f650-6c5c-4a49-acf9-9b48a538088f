package com.ms.bp.infrastructure.repository.dao;

import com.ms.bp.domain.master.model.UnitGroupInfo;
import com.ms.bp.shared.common.db.JdbcTemplate;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.SQLException;
import java.util.List;
import java.util.Optional;

/**
 * ユニットマスタデータアクセス実装
 * M_UNITMST（ユニットマスタ）への具体的なデータアクセスを実装
 */
public class UnitMasterDataAccess {
    private static final Logger logger = LoggerFactory.getLogger(UnitMasterDataAccess.class);

    // SQL定数
    private static final String EXISTS_BY_UNIT_CODE_SQL = """
        SELECT 1
        FROM M_UNITMST
        WHERE UNIT_CODE = ?
        AND TO_CHAR(CURRENT_DATE, 'YYYYMMDD') >= KSHB
        AND TO_CHAR(CURRENT_DATE, 'YYYYMMDD') <= SHRYB
        AND SHIYO_KNSH_KUBUN = '0'
        LIMIT 1
        """;
    private static final String FIND_UNIT_GROUP_INFO_SQL = """
        SELECT
            u.UNIT_CODE,
            u.unit_mei_tnshk_kanji,
            u.GROUP_CODE,
            g.group_mei_tnshk_kanji
        FROM M_UNITMST u
        LEFT JOIN M_GROUPMST g ON u.GROUP_CODE = g.GROUP_CODE
            AND TO_CHAR(CURRENT_DATE, 'YYYYMMDD') >= g.KSHB AND g.SHRYB >= TO_CHAR(CURRENT_DATE, 'YYYYMMDD')
            AND g.SHIYO_KNSH_KUBUN = '0'
        WHERE u.UNIT_CODE = ?
        AND TO_CHAR(CURRENT_DATE, 'YYYYMMDD') >= u.KSHB AND u.SHRYB >= TO_CHAR(CURRENT_DATE, 'YYYYMMDD')
        AND u.SHIYO_KNSH_KUBUN = '0'
        ORDER BY u.KSHB DESC, g.KSHB DESC
        LIMIT 1
        """;
    private final JdbcTemplate jdbcTemplate;

    public UnitMasterDataAccess(JdbcTemplate jdbcTemplate) {
        this.jdbcTemplate = jdbcTemplate;
    }

    /**
     * ユニットコードでユニットマスタの存在チェック
     *
     * @param unitCode ユニットコード
     * @return 存在する場合true、存在しない場合false
     * @throws SQLException データベースアクセスエラー
     */
    public boolean existsByUnitCode(String unitCode) throws SQLException {
        logger.debug("ユニットマスタ存在チェック開始: ユニットコード={}", unitCode);

        List<Integer> results = jdbcTemplate.query(
            EXISTS_BY_UNIT_CODE_SQL,
            new Object[]{unitCode},
            rs -> rs.getInt(1)
        );

        boolean exists = !results.isEmpty();
        logger.debug("ユニットマスタ存在チェック完了: ユニットコード={}, 存在={}", unitCode, exists);

        return exists;
    }

    /**
     * ユニットコードでユニット・グループ統合情報を取得（パフォーマンス最適化版）
     * 単一クエリでユニットマスタとグループマスタを結合して取得
     *
     * @param unitCode ユニットコード
     * @return ユニット・グループ統合情報（該当データがない場合は空のOptional）
     * @throws SQLException データベースアクセスエラー
     */
    public Optional<UnitGroupInfo> findUnitGroupInfoByUnitCode(String unitCode) throws SQLException {
        logger.debug("ユニット・グループ統合情報取得開始: ユニットコード={}", unitCode);

        List<UnitGroupInfo> results = jdbcTemplate.query(
                FIND_UNIT_GROUP_INFO_SQL,
                new Object[]{unitCode},
                rs -> UnitGroupInfo.builder()
                        .unitCode(rs.getString("UNIT_CODE"))
                        .unitName(rs.getString("unit_mei_tnshk_kanji"))
                        .groupCode(rs.getString("GROUP_CODE"))
                        .groupName(rs.getString("group_mei_tnshk_kanji"))
                        .build()
        );

        Optional<UnitGroupInfo> result = results.isEmpty() ? Optional.empty() : Optional.of(results.get(0));

        if (result.isPresent()) {
            logger.debug("ユニット・グループ統合情報取得完了: ユニットコード={}, ユニット名={}, グループ名={}",
                    unitCode, result.get().getUnitName(), result.get().getGroupName());
        } else {
            logger.debug("ユニット・グループ統合情報が見つかりませんでした: ユニットコード={}", unitCode);
        }

        return result;
    }
}
