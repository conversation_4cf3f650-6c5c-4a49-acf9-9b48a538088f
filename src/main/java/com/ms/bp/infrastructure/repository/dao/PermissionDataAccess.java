package com.ms.bp.infrastructure.repository.dao;

import com.ms.bp.domain.master.model.AreaInfo;
import com.ms.bp.domain.permission.model.Permission;
import com.ms.bp.domain.permission.model.PersonalPermission;
import com.ms.bp.shared.common.constants.BusinessConstants;
import com.ms.bp.shared.common.db.JdbcTemplate;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;

/**
 * 権限リポジトリデータアクセス実装
 * 権限関連のデータベースアクセスを担当する
 */
public class PermissionDataAccess {
    private static final Logger logger = LoggerFactory.getLogger(PermissionDataAccess.class);

    private final JdbcTemplate jdbcTemplate;
    
    public PermissionDataAccess(JdbcTemplate jdbcTemplate) {
        this.jdbcTemplate = jdbcTemplate;
    }
    
    // ==================== 権限マスタ関連 ====================
    /**
     * 共通ルールから権限情報を一条SQLで効率的に取得
     *
     * @param systemOperationCompanyCode システム運用企業コード
     * @param userUnitCode ユーザーのユニットコード
     * @param areaCode エリアコード
     * @return 権限情報のリスト（権限がない場合は空のリスト）
     */
    public List<Permission> findCommonPermissions(String systemOperationCompanyCode,
                                                         String userUnitCode,
                                                         String areaCode) throws SQLException {

        // 動的SQLを構築
        StringBuilder sql = new StringBuilder();
        sql.append("""
            SELECT
                ken.KEN_CODE,
                ken.SYSTM_UNYO_KIGYO_CODE,
                ken.HANT_PTTRN,
                ken.HANT_CODE,
                ken.KSHB,
                ken.SHRYB
            FROM M_KENGEN ken
            WHERE ken.SYSTM_UNYO_KIGYO_CODE = ?
            """);

        sql.append("""            
            AND ken.KSHB <= to_char(CURRENT_DATE, 'YYYYMMDD')
            AND ken.SHRYB >= to_char(CURRENT_DATE, 'YYYYMMDD')
            AND (
                -- M_KEN_RULEと連表してチェック
                EXISTS (
                    SELECT 1
                    FROM M_KEN_RULE rule
                    WHERE rule.SYSTM_UNYO_KIGYO_CODE = ken.SYSTM_UNYO_KIGYO_CODE
                    AND rule.HANT_CODE = ken.HANT_CODE
                    AND rule.KSHB <= to_char(CURRENT_DATE, 'YYYYMMDD')
                    AND rule.SHRYB >= to_char(CURRENT_DATE, 'YYYYMMDD')
                    AND (
                        -- 判定区分 1（ユニットコード判定）
                        (ken.HANT_PTTRN  = '1' AND rule.SHR_CODE = ?)
                        OR
                        -- ルール種類2（エリアコード判定）
                        (ken.HANT_PTTRN = '2' AND rule.SHR_CODE = ?)
                    )
                )
            )
            ORDER BY ken.KEN_CODE
            """);

        // パラメータを構築
        List<Object> params = new ArrayList<>();
        params.add(systemOperationCompanyCode);
        params.add(userUnitCode);
        params.add(areaCode);

        return jdbcTemplate.query(sql.toString(),
            params.toArray(),
            (rs) -> {
                Permission permission = new Permission();
                permission.setPermissionCode(rs.getString("KEN_CODE"));
                permission.setSystemOperationCompanyCode(rs.getString("SYSTM_UNYO_KIGYO_CODE"));
                permission.setHantPttrn(rs.getString("HANT_PTTRN"));
                permission.setHantCode(rs.getString("HANT_CODE"));
                permission.setPermissionStartDate(rs.getString("KSHB"));
                permission.setPermissionEndDate(rs.getString("SHRYB"));
                return permission;
            });
    }
    
    // ==================== 個人権限設定マスタ関連 ====================
    /**
     * 指定条件で個人権限を検索
     * @param shainCode 社員コード
     * @param systemOperationCompanyCode システム運用企業コード
     * @return 個人権限リスト
     */
    public List<PersonalPermission> findPersonalPermissions(String shainCode,
                                                            String systemOperationCompanyCode) throws SQLException {
        StringBuilder sql = new StringBuilder();
        sql.append("""
            SELECT
                person.KEN_CODE,
                person.SYSTM_UNYO_KIGYO_CODE,
                person.KYOKA_FLAG,
                person.SHAIN_CODE,
                HANT_CODE
            FROM M_KEN_PERSON person
            Inner Join M_KENGEN ON M_KENGEN.KEN_CODE = person.KEN_CODE
            WHERE SHAIN_CODE = ?
            AND person.SYSTM_UNYO_KIGYO_CODE = ?
            """);


        // 有効期限チェック
        sql.append(" AND person.KSHB <= to_char(CURRENT_DATE, 'YYYYMMDD') ");
        sql.append(" AND person.SHRYB >= to_char(CURRENT_DATE, 'YYYYMMDD') ");

        sql.append(" ORDER BY person.KEN_CODE");
        // パラメータを構築
        List<Object> paramList = new ArrayList<>();
        paramList.add(shainCode);
        paramList.add(systemOperationCompanyCode);

        return jdbcTemplate.query(sql.toString(), paramList.toArray(), (rs) -> {
            PersonalPermission permission = new PersonalPermission();
            permission.setPermissionCode(rs.getString("KEN_CODE"));
            permission.setSystemOperationCompanyCode(rs.getString("SYSTM_UNYO_KIGYO_CODE"));
            permission.setShainCode(rs.getString("SHAIN_CODE"));
            permission.setPermissionFlag(rs.getString("KYOKA_FLAG"));
            permission.setHantCode(rs.getString("HANT_CODE"));
            return permission;
        });
    }

    // ==================== 権限ルールマスタ関連 ====================
    /**
     * 指定された判定コードとシステム運用企業コードに対応する種類コードリストを取得
     * M_KEN_RULEテーブルから条件に一致する種類コードを検索
     *
     * @param hantCode 判定コード
     * @param systemOperationCompanyCode システム運用企業コード
     * @return 種類コードのリスト（該当データがない場合は空のリスト）
     * @throws SQLException データベースアクセスエラー
     */
    public List<String> findRuleTypeCodesByMsStrategyCorp(String hantCode, String systemOperationCompanyCode) throws SQLException {
        String sql = """
            SELECT shr_code
            FROM M_KEN_RULE
            WHERE HANT_CODE = ?
            AND SYSTM_UNYO_KIGYO_CODE = ?
            AND KSHB <= to_char(CURRENT_DATE, 'YYYYMMDD')
            AND SHRYB >= to_char(CURRENT_DATE, 'YYYYMMDD')
            ORDER BY shr_code
            """;

        logger.debug("権限ルール種類コード取得SQL実行: HANT_CODE={}, SYSTM_UNYO_KIGYO_CODE={}",
                    hantCode, systemOperationCompanyCode);

        Object[] params = {hantCode, systemOperationCompanyCode};

        List<String> typeCodes = jdbcTemplate.query(sql, params, rs -> rs.getString("shr_code"));

        logger.debug("権限ルール種類コード取得結果: 件数={}", typeCodes.size());

        return typeCodes;
    }

    /**
     * エリア担当者権限に基づくエリア情報リストを取得
     * 権限ルールマスタから判定コード（エリア担当者）の種類コードを取得し、
     * 組織エリアマスタと関連付けてエリア情報を取得する
     *
     * @param systemOperationCompanyCode システム運用企業コード
     * @return エリア情報リスト（該当データがない場合は空のリスト）
     * @throws SQLException データベースアクセスエラー
     */
    public List<AreaInfo> findAreaInfosByAreaTantoshaPermission(String systemOperationCompanyCode) throws SQLException {
        String sql = """
            SELECT DISTINCT
                rule.SHR_CODE as area_code,
                msa_latest.area_mei_tnshk_kanji as area_name
            FROM M_KEN_RULE rule
            LEFT JOIN LATERAL (
                SELECT
                    REGEXP_REPLACE(msa.area_mei_tnshk_kanji, '\\s', '', 'g') as area_mei_tnshk_kanji
                FROM
                    M_SOSHIKIAREAMST msa
                WHERE
                    msa.area_code = rule.SHR_CODE
                    -- JOIN条件: 現在有効で、かつ使用禁止でないレコードを対象にする
                    AND TO_CHAR(CURRENT_DATE, 'YYYYMMDD') >= msa.kshb
                    AND TO_CHAR(CURRENT_DATE, 'YYYYMMDD') <= msa.shryb
                    AND msa.shiyo_knsh_kubun = '0'
                -- 開始日が最も新しいものを取得するために降順で並び替え
                ORDER BY
                    msa.kshb DESC, msa.sub_area_code
                LIMIT 1
            ) AS msa_latest ON TRUE
            WHERE rule.HANT_CODE = ?
                AND rule.SYSTM_UNYO_KIGYO_CODE = ?
                AND rule.KSHB <= to_char(CURRENT_DATE, 'YYYYMMDD')
                AND rule.SHRYB >= to_char(CURRENT_DATE, 'YYYYMMDD')
                AND msa_latest.area_mei_tnshk_kanji IS NOT NULL
            ORDER BY rule.SHR_CODE
            """;

        logger.debug("エリア担当者権限エリア情報取得SQL実行: HANT_CODE={}, SYSTM_UNYO_KIGYO_CODE={}",
                    BusinessConstants.AREA_TANTOSHA_HANT_CODE, systemOperationCompanyCode);

        Object[] params = {BusinessConstants.AREA_TANTOSHA_HANT_CODE, systemOperationCompanyCode};

        List<AreaInfo> areaInfos = jdbcTemplate.query(sql, params, rs -> {
            String tempAreaCode = rs.getString("area_code");
            String areaCode = (tempAreaCode != null) ? tempAreaCode.trim() : tempAreaCode;
            String areaName = rs.getString("area_name");
            return new AreaInfo(areaCode, areaName);
        });

        logger.debug("エリア担当者権限エリア情報取得結果: {}件のエリア情報が見つかりました", areaInfos.size());

        return areaInfos;
    }

    // ==================== システム管理者権限関連 ====================

    /**
     * システム管理者用の全有効本社権限を取得
     * 権限マスタから有効期間内の全権限を取得し、本社権限（権限コード3桁目='H'）のみを抽出
     *
     * @param systemOperationCompanyCode システム運用企業コード
     * @return 本社権限リスト（該当データがない場合は空のリスト）
     * @throws SQLException データベースアクセスエラー
     */
    public List<Permission> findAllValidHeadOfficePermissions(String systemOperationCompanyCode) throws SQLException {
        String sql = """
            SELECT DISTINCT
                KEN_CODE
            FROM M_KENGEN
            WHERE SYSTM_UNYO_KIGYO_CODE = ?
            AND KSHB <= to_char(CURRENT_DATE, 'YYYYMMDD')
            AND SHRYB >= to_char(CURRENT_DATE, 'YYYYMMDD')
            AND LENGTH(KEN_CODE) >= 3
            AND SUBSTRING(KEN_CODE, 3, 1) = 'H'
            ORDER BY KEN_CODE
            """;

        logger.debug("システム管理者本社権限取得SQL実行: systemOperationCompanyCode={}",
                    systemOperationCompanyCode);

        Object[] params = {systemOperationCompanyCode};

        List<Permission> permissions = jdbcTemplate.query(sql, params, rs -> {
            Permission permission = new Permission();
            permission.setPermissionCode(rs.getString("KEN_CODE"));
            return permission;
        });

        logger.debug("システム管理者本社権限取得結果: {}件の権限が見つかりました", permissions.size());

        return permissions;
    }
}