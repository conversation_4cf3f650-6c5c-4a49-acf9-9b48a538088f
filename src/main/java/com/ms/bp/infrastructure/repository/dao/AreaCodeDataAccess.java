package com.ms.bp.infrastructure.repository.dao;

import com.ms.bp.domain.master.model.AreaInfo;
import com.ms.bp.shared.common.db.JdbcTemplate;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.SQLException;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Stream;

/**
 * エリアコードデータアクセス実装
 * M_SK_KKK_AREA（採算管理単位計画策定エリアマスタ）への具体的なデータアクセスを実装
 */
public class AreaCodeDataAccess {
    private static final Logger logger = LoggerFactory.getLogger(AreaCodeDataAccess.class);

    private final JdbcTemplate jdbcTemplate;

    public AreaCodeDataAccess(JdbcTemplate jdbcTemplate) {
        this.jdbcTemplate = jdbcTemplate;
    }

    /**
     * 年度とシステム運用企業コードでエリアコードリストを取得
     * 
     * @param nendo 年度（YYYY形式）
     * @param systemOperationCompanyCode システム運用企業コード
     * @return エリアコードリスト（エリア表示順でソート済み）
     * @throws SQLException データベースアクセスエラー
     */
    public List<String> findAreaCodesByNendo(String nendo, String systemOperationCompanyCode) throws SQLException {
        String sql = """
            SELECT area_code 
            FROM M_SK_KKK_AREA 
            WHERE nendo = ?
              AND systm_unyo_kigyo_code = ?
            ORDER BY area_order
            """;
        
        logger.debug("エリアコード取得SQL実行: 年度={}, 企業コード={}", nendo, systemOperationCompanyCode);

        Object[] params = {nendo, systemOperationCompanyCode};

        try {
            List<String> areaCodes = jdbcTemplate.query(sql, params, rs -> rs.getString("area_code"));

            logger.debug("エリアコード取得結果: 件数={}, リスト={}", areaCodes.size(), areaCodes);

            if (areaCodes.isEmpty()) {
                logger.warn("M_SK_KKK_AREAテーブルからエリアコードが取得できませんでした: 年度={}, 企業コード={}",
                           nendo, systemOperationCompanyCode);
            }

            return areaCodes;

        } catch (SQLException e) {
            logger.error("エリアコード取得エラー: SQL={}, 年度={}, 企業コード={}",
                        sql, nendo, systemOperationCompanyCode, e);
            throw e;
        }
    }

    /**
     * 年度とシステム運用企業コードちエリアコードでエリア情報リストを取得
     * エリアコードが重複した場合は、重複削除要
     * M_SK_KKK_AREAとM_SOSHIKIAREAMSTを関連付けてエリアコードとエリア名称を取得
     *
     * @param nendo 年度（YYYY形式）
     * @param systemOperationCompanyCode システム運用企業コード
     * @param areaCodes エリアコードリスト
     * @return エリア情報リスト（エリア表示順でソート済み）
     * @throws SQLException データベースアクセスエラー
     */
    public List<AreaInfo> findAreaInfosForMsth(String nendo, String systemOperationCompanyCode, List<String> areaCodes) throws SQLException {
        String sql = """
                     WITH
                      order_area_info AS (
                        SELECT
                          areas.*,
                          msa.area_mei_kanji,
                          msa.area_mei_tnshk_kanji,
                          ROW_NUMBER() OVER(PARTITION BY areas.area_code ORDER BY msa.kshb DESC,msa.sub_area_code) num
                        from
                          (
                            SELECT
                                distinct
                                area_code
                            FROM M_SK_KKK_AREA
                            WHERE nendo = ?
                                  %S
                          ) as areas
                          LEFT JOIN  M_SOSHIKIAREAMST msa ON areas.area_code=msa.area_code
                               AND TO_CHAR(CURRENT_DATE, 'YYYYMMDD') >= msa.kshb
                               AND TO_CHAR(CURRENT_DATE, 'YYYYMMDD') <= msa.shryb
                               AND msa.shiyo_knsh_kubun = '0'
                      )
                    
                      SELECT
                        area_code,
                        REGEXP_REPLACE(area_mei_tnshk_kanji, '\\s', '', 'g') as area_mei_tnshk_kanji
                      FROM
                        order_area_info
                      WHERE
                        num = 1
                """;

        // エリアコードの数だけ?プレースホルダーを生成
        String areaStr = String.join(",", Collections.nCopies(areaCodes.size(), "?"));
        String systemCondition = !Objects.equals(systemOperationCompanyCode, "") ?String.format(" or systm_unyo_kigyo_code = '%s'", systemOperationCompanyCode):"";
        String areaCondition = String.format(" and (area_code IN (%s) %s)",areaStr,systemCondition);
        sql = String.format(sql,areaCondition);

        logger.debug("エリア情報取得SQL実行: 年度={}, 企業コード={}, エリアコード={}", nendo,systemOperationCompanyCode,areaStr);

        Object[] params = Stream.concat(Stream.of(nendo), areaCodes.stream()).toArray();

        try {
            List<AreaInfo> areaInfos = jdbcTemplate.query(sql, params, rs -> {
                String areaCode = rs.getString("area_code");
                String areaTnshkName = rs.getString("area_mei_tnshk_kanji");
                return new AreaInfo(areaCode,areaTnshkName);
            });

            logger.debug("エリア情報取得結果: 件数={},areas={}", areaInfos.size(),areaInfos);

            if (areaInfos.isEmpty()) {
                logger.warn("M_SK_KKK_AREAテーブルからエリア情報が取得できませんでした: 年度={}, 企業コード={}",
                        nendo, systemOperationCompanyCode);
            }

            return areaInfos;

        } catch (SQLException e) {
            logger.error("エリア情報取得エラー: SQL={}, 年度={}, 企業コード={}",
                    sql, nendo, systemOperationCompanyCode, e);
            throw e;
        }
    }

    /**
     * エリアコードでエリア名称を取得
     * M_SOSHIKIAREAMST（組織エリアマスタ）からエリア名短縮漢字を取得する
     *
     * @param areaCode エリアコード（4桁）
     * @return エリア名称（該当データがない場合は空のOptional）
     * @throws SQLException データベースアクセスエラー
     */
    public Optional<String> findAreaNameByAreaCode(String areaCode) throws SQLException {
        String sql = """
            SELECT
                area_mei_tnshk_kanji
            FROM M_SOSHIKIAREAMST
            WHERE area_code = ?
            AND TO_CHAR(CURRENT_DATE, 'YYYYMMDD') >= kshb
            AND TO_CHAR(CURRENT_DATE, 'YYYYMMDD') <= shryb
            AND shiyo_knsh_kubun = '0'
            ORDER BY kshb DESC, sub_area_code
            LIMIT 1
            """;

        logger.debug("エリア名称取得SQL実行: エリアコード={}", areaCode);

        Object[] params = {areaCode};

        try {
            List<String> areaNames = jdbcTemplate.query(sql, params, rs -> rs.getString("area_mei_tnshk_kanji"));

            Optional<String> result = areaNames.isEmpty() ? Optional.empty() : Optional.of(areaNames.get(0));

            if (result.isPresent()) {
                logger.debug("エリア名称取得完了: エリアコード={}, エリア名称={}", areaCode, result.get());
            } else {
                logger.debug("エリア名称が見つかりませんでした: エリアコード={}", areaCode);
            }

            return result;

        } catch (SQLException e) {
            logger.error("エリア名称取得エラー: SQL={}, エリアコード={}", sql, areaCode, e);
            throw e;
        }
    }
}
