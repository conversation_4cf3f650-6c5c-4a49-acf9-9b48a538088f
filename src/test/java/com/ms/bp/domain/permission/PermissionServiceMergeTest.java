package com.ms.bp.domain.permission;

import com.ms.bp.domain.permission.model.Permission;
import com.ms.bp.domain.permission.model.PersonalPermission;
import com.ms.bp.domain.permission.model.UserPermissionInfo;
import com.ms.bp.domain.permission.repository.PermissionRepository;
import com.ms.bp.domain.user.model.UserInfo;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.when;

/**
 * PermissionService権限統合処理単体テスト
 * 共通権限と個人権限の統合ロジックを検証
 */
@ExtendWith(MockitoExtension.class)
class PermissionServiceMergeTest {

    @Mock
    private PermissionRepository permissionRepository;

    private PermissionService permissionService;

    @BeforeEach
    void setUp() {
        permissionService = new PermissionService(permissionRepository);
    }

    @Test
    @DisplayName("権限統合処理 - 基本的な統合フロー")
    void testMergePermissions_BasicFlow() throws Exception {
        // テストデータ準備
        List<Permission> commonPermissions = createCommonPermissions();
        List<PersonalPermission> personalPermissions = createPersonalPermissions();

        // リフレクションでprivateメソッドを呼び出し
        Method mergeMethod = PermissionService.class.getDeclaredMethod(
                "mergePermissions", List.class, List.class);
        mergeMethod.setAccessible(true);

        // 実行
        @SuppressWarnings("unchecked")
        List<Permission> result = (List<Permission>) mergeMethod.invoke(
                permissionService, commonPermissions, personalPermissions);

        // 検証
        assertThat(result).hasSize(3);
        
        // 共通権限PERM001は個人権限で禁止されているため削除される
        assertThat(result.stream().noneMatch(p -> "PERM001".equals(p.getPermissionCode())))
                .isTrue();
        
        // 共通権限PERM002は個人権限で許可されているため保持される
        assertThat(result.stream().anyMatch(p -> "PERM002".equals(p.getPermissionCode())))
                .isTrue();
        
        // 共通権限PERM003は個人権限設定なしのため保持される
        assertThat(result.stream().anyMatch(p -> "PERM003".equals(p.getPermissionCode())))
                .isTrue();
        
        // 個人権限PERM004は許可フラグのため追加される
        assertThat(result.stream().anyMatch(p -> "PERM004".equals(p.getPermissionCode())))
                .isTrue();
        
        // 個人権限PERM005は禁止フラグのため追加されない
        assertThat(result.stream().noneMatch(p -> "PERM005".equals(p.getPermissionCode())))
                .isTrue();
    }

    @Test
    @DisplayName("権限統合処理 - 全体フロー統合テスト")
    void testGetUserPermissions_IntegratedFlow() {
        // テストデータ準備
        UserInfo userInfo = createTestUserInfo();
        List<Permission> commonPermissions = createCommonPermissions();
        List<PersonalPermission> personalPermissions = createPersonalPermissions();

        // モック設定
        when(permissionRepository.findCommonPermissions(
                userInfo.getSystemOperationCompanyCode(),
                userInfo.getUnitCode(),
                userInfo.getAreaCode()))
                .thenReturn(commonPermissions);

        when(permissionRepository.findPersonalPermissions(
                userInfo.getShainCode(),
                userInfo.getSystemOperationCompanyCode()))
                .thenReturn(personalPermissions);

        // 実行
        List<UserPermissionInfo> result = permissionService.getUserPermissions(userInfo);

        // 検証
        assertThat(result).isNotEmpty();
        
        // 権限統合処理が正しく実行されていることを確認
        List<String> permissionCodes = result.stream()
                .map(UserPermissionInfo::getPermissionCode)
                .toList();
        
        // 禁止された権限は含まれない
        assertThat(permissionCodes).doesNotContain("PERM001");
        
        // 許可された権限は含まれる
        assertThat(permissionCodes).contains("PERM002", "PERM003", "PERM004");
    }

    /**
     * テスト用共通権限データ作成
     */
    private List<Permission> createCommonPermissions() {
        List<Permission> permissions = new ArrayList<>();
        
        // 共通権限1: 個人権限で禁止される予定
        Permission perm1 = new Permission();
        perm1.setPermissionCode("PERM001");
        perm1.setSystemOperationCompanyCode("001");
        perm1.setHantCode("H001");
        permissions.add(perm1);
        
        // 共通権限2: 個人権限で許可される予定
        Permission perm2 = new Permission();
        perm2.setPermissionCode("PERM002");
        perm2.setSystemOperationCompanyCode("001");
        perm2.setHantCode("H002");
        permissions.add(perm2);
        
        // 共通権限3: 個人権限設定なし
        Permission perm3 = new Permission();
        perm3.setPermissionCode("PERM003");
        perm3.setSystemOperationCompanyCode("001");
        perm3.setHantCode("H003");
        permissions.add(perm3);
        
        return permissions;
    }

    /**
     * テスト用個人権限データ作成
     */
    private List<PersonalPermission> createPersonalPermissions() {
        List<PersonalPermission> permissions = new ArrayList<>();
        
        // 個人権限1: 共通権限PERM001を禁止
        PersonalPermission personal1 = new PersonalPermission();
        personal1.setPermissionCode("PERM001");
        personal1.setSystemOperationCompanyCode("001");
        personal1.setShainCode("EMP001");
        personal1.setPermissionFlag("1"); // 禁止
        personal1.setHantCode("H001");
        permissions.add(personal1);
        
        // 個人権限2: 共通権限PERM002を許可
        PersonalPermission personal2 = new PersonalPermission();
        personal2.setPermissionCode("PERM002");
        personal2.setSystemOperationCompanyCode("001");
        personal2.setShainCode("EMP001");
        personal2.setPermissionFlag("0"); // 許可
        personal2.setHantCode("H002");
        permissions.add(personal2);
        
        // 個人権限3: 新規権限PERM004を追加（許可）
        PersonalPermission personal3 = new PersonalPermission();
        personal3.setPermissionCode("PERM004");
        personal3.setSystemOperationCompanyCode("001");
        personal3.setShainCode("EMP001");
        personal3.setPermissionFlag("0"); // 許可
        personal3.setHantCode("H004");
        permissions.add(personal3);
        
        // 個人権限4: 新規権限PERM005を追加（禁止）
        PersonalPermission personal4 = new PersonalPermission();
        personal4.setPermissionCode("PERM005");
        personal4.setSystemOperationCompanyCode("001");
        personal4.setShainCode("EMP001");
        personal4.setPermissionFlag("1"); // 禁止
        personal4.setHantCode("H005");
        permissions.add(personal4);
        
        return permissions;
    }

    /**
     * テスト用ユーザー情報作成
     */
    private UserInfo createTestUserInfo() {
        UserInfo userInfo = new UserInfo();
        userInfo.setShainCode("EMP001");
        userInfo.setSystemOperationCompanyCode("001");
        userInfo.setUnitCode("UNIT01");
        userInfo.setAreaCode("AREA01");
        return userInfo;
    }
}
