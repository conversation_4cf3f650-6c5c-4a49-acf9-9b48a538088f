package com.ms.bp.domain.permission;

import com.ms.bp.application.PermissionApplicationService;
import com.ms.bp.domain.permission.model.UserPermissionInfo;
import com.ms.bp.domain.user.model.UserInfo;
import com.ms.bp.interfaces.dto.response.UserPermissionsResponseV2;
import com.ms.bp.shared.common.constants.BusinessConstants;
import com.ms.bp.util.TestDataManager;
import org.junit.jupiter.api.*;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.jupiter.MockitoExtension;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.*;

/**
 * 権限システム集成テスト
 * 権限システムの全体的な業務フローを検証する統合テストクラス
 *
 * テスト対象：
 * - PermissionService: 権限ドメインサービス
 * - PermissionApplicationService: 権限アプリケーションサービス
 *
 * テストシナリオ：
 * 【基本権限取得フロー】
 * - 正常な権限取得フロー（共通権限と個人権限の統合）
 * - 権限重複排除処理（本社権限優先ルール）
 * - 操作区分別権限フィルタリング（アップロード/ダウンロード）
 * - 権限なしユーザーの処理
 *
 * 【新業務要件対応（V2レスポンス）】
 * - システム管理者権限の判定と取得
 * - 一般ユーザーの兼務情報を含むロールベース権限取得
 * - パラメータ提供時のシステム管理者フラグ処理
 * - 操作区分別フィルタリング（V2レスポンス）
 *
 * 【権限コード解析・検証】
 * - 権限コード解析ロジックの検証
 * - 権限重複排除処理の詳細検証
 *
 * テストデータ管理：
 * - TestDataManagerを使用してExcelファイルからテストデータを自動挿入・削除
 * - 各テストケース実行前後でデータベース状態を適切に管理
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("権限システム集成テスト")
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
public class PermissionSystemIntegrationTest {

    private static final Logger logger = LoggerFactory.getLogger(PermissionSystemIntegrationTest.class);

    // テスト対象サービス（実際のインスタンス）
    private PermissionApplicationService permissionApplicationService;

    // テストデータ管理
    private TestDataManager testDataManager;
    private Map<String, List<Map<String, Object>>> insertedDataTracker;

    @BeforeEach
    void setUp() {
        logger.info("=== 権限システム集成テストセットアップ開始 ===");

        try {
            // Mockito初期化
            MockitoAnnotations.openMocks(this);

            // AWS設定を環境変数で設定してリソース初期化エラーを回避
            System.setProperty("aws.region", "ap-northeast-1");

            // テスト対象サービスの初期化
            permissionApplicationService = new PermissionApplicationService();

            // 権限システム用テストデータ管理器の初期化
            testDataManager = new TestDataManager("permission_system_test_data.xlsx");

            // Excelからテストデータを読み込んでデータベースに挿入
            insertedDataTracker = testDataManager.insertAllTestData();

            logger.info("=== 権限システム集成テストセットアップ完了 ===");

        } catch (Exception e) {
            logger.error("権限システムテストセットアップエラー: {}", e.getMessage(), e);
            throw new RuntimeException("権限システムテストセットアップに失敗しました", e);
        }
    }

    @AfterEach
    void tearDown() {
        logger.info("=== 権限システム集成テストクリーンアップ開始 ===");

        if (insertedDataTracker != null) {
            logger.info("権限システムテストデータクリーンアップを実行します: {} テーブル", insertedDataTracker.size());
            testDataManager.deleteAllTestData(insertedDataTracker);
            logger.info("権限システムテストデータクリーンアップが完了しました");
        }

        logger.info("=== 権限システム集成テストクリーンアップ完了 ===");
    }

    // ==================== 基本権限取得フロー統合テスト ====================

    /**
     * 権限システム正常フロー統合テスト
     * 共通権限と個人権限の統合、権限重複排除処理を含む完全なフローを検証
     */
    @Test
    @Order(1)
    @DisplayName("権限システム正常フロー_共通権限と個人権限統合テスト")
    void testPermissionSystem_正常フロー統合() {
        logger.info("=== 権限システム正常フロー統合テスト開始 ===");

        try {
            // Given: テスト用ユーザー情報を準備
            UserInfo testUserInfo = createTestUserInfo("115651", "100001", "12345", "0202");

            // When: ユーザー権限を取得（操作区分指定なし）- V2レスポンス形式を使用
            UserPermissionsResponseV2 response = permissionApplicationService.getUserPermissions(
                    testUserInfo, "12345", "41", "0202", "G001", "0", null);

            // Then: 結果を検証
            assertNotNull(response, "権限レスポンスが取得できること");
            assertNotNull(response.getRoleList(), "ロールリストが設定されていること");
            assertThat(response.getRoleList()).isNotEmpty();

            // 最初のロールから権限リストを取得
            UserPermissionsResponseV2.RoleInfo roleInfo = response.getRoleList().get(0);
            List<UserPermissionsResponseV2.PermissionInfo> permissions = roleInfo.getPermissionList();
            logger.info("取得された権限数: {}", permissions.size());

            // 期待される権限の検証（V2形式）
            verifyExpectedPermissionsV2(permissions);

            // 権限重複排除処理の検証（本社権限優先ルール）
            verifyPermissionDeduplicationV2(permissions);

            logger.info("✅ 権限システム正常フロー統合テスト完了");

        } catch (Exception e) {
            logger.error("権限システム正常フローテストエラー: {}", e.getMessage(), e);
            fail("権限システム正常フローテストに失敗しました: " + e.getMessage());
        }
    }

    /**
     * ダウンロード権限フィルタリングテスト
     * 操作区分でダウンロード権限のみを取得する処理を検証
     */
    @Test
    @Order(2)
    @DisplayName("権限システム_ダウンロード権限フィルタリングテスト")
    void testPermissionSystem_ダウンロード権限フィルタリング() {
        logger.info("=== ダウンロード権限フィルタリングテスト開始 ===");

        try {
            // Given: テスト用ユーザー情報を準備
            UserInfo testUserInfo = createTestUserInfo("115651", "100001", "12345", "0202");

            // When: ダウンロード権限のみを取得
            UserPermissionsResponseV2 response = permissionApplicationService.getUserPermissions(
                    testUserInfo, null, null, null, null, "0", BusinessConstants.OPERATION_DOWNLOAD_CODE);

            // Then: ダウンロード権限のみが返されることを検証
            assertNotNull(response, "権限レスポンスが取得できること");
            assertNotNull(response.getRoleList(), "ロールリストが設定されていること");
            assertThat(response.getRoleList()).isNotEmpty();

            UserPermissionsResponseV2.RoleInfo roleInfo = response.getRoleList().get(0);
            List<UserPermissionsResponseV2.PermissionInfo> permissions = roleInfo.getPermissionList();

            // 全ての権限がダウンロード権限であることを確認（V2では"2"がダウンロード）
            permissions.forEach(permission -> {
                assertThat(permission.getOperationDivision())
                        .isEqualTo("2"); // V2: 2=ダウンロード
                logger.debug("V2ダウンロード権限確認: {}", permission.getPermissionCode());
            });

            logger.info("✅ ダウンロード権限フィルタリングテスト完了: {}件", permissions.size());

        } catch (Exception e) {
            logger.error("ダウンロード権限フィルタリングテストエラー: {}", e.getMessage(), e);
            fail("ダウンロード権限フィルタリングテストに失敗しました: " + e.getMessage());
        }
    }

    /**
     * アップロード権限フィルタリングテスト
     * 操作区分でアップロード権限のみを取得する処理を検証
     */
    @Test
    @Order(3)
    @DisplayName("権限システム_アップロード権限フィルタリングテスト")
    void testPermissionSystem_アップロード権限フィルタリング() {
        logger.info("=== アップロード権限フィルタリングテスト開始 ===");

        try {
            // Given: テスト用ユーザー情報を準備
            UserInfo testUserInfo = createTestUserInfo("115651", "100001", "12345", "0202");

            // When: アップロード権限のみを取得
            UserPermissionsResponseV2 response = permissionApplicationService.getUserPermissions(
                    testUserInfo, null, null, null, null, "0", BusinessConstants.OPERATION_UPLOAD_CODE);

            // Then: アップロード権限のみが返されることを検証
            assertNotNull(response, "権限レスポンスが取得できること");
            assertNotNull(response.getRoleList(), "ロールリストが設定されていること");
            assertThat(response.getRoleList()).isNotEmpty();

            UserPermissionsResponseV2.RoleInfo roleInfo = response.getRoleList().get(0);
            List<UserPermissionsResponseV2.PermissionInfo> permissions = roleInfo.getPermissionList();

            // 全ての権限がアップロード権限であることを確認（V2では"1"がアップロード）
            permissions.forEach(permission -> {
                assertThat(permission.getOperationDivision())
                        .isEqualTo("1"); // V2: 1=アップロード
                logger.debug("V2アップロード権限確認: {}", permission.getPermissionCode());
            });

            logger.info("✅ アップロード権限フィルタリングテスト完了: {}件", permissions.size());

        } catch (Exception e) {
            logger.error("アップロード権限フィルタリングテストエラー: {}", e.getMessage(), e);
            fail("アップロード権限フィルタリングテストに失敗しました: " + e.getMessage());
        }
    }

    /**
     * 権限なしユーザーテスト
     * 権限が設定されていないユーザーの処理を検証
     */
    @Test
    @Order(4)
    @DisplayName("権限システム_権限なしユーザーテスト")
    void testPermissionSystem_権限なしユーザー() {
        logger.info("=== 権限なしユーザーテスト開始 ===");

        try {
            // Given: 権限が設定されていないユーザー情報を準備
            UserInfo noPermissionUser = createTestUserInfo("999999", "100001", "99999", "9999");

            // When: ユーザー権限を取得
            UserPermissionsResponseV2 response = permissionApplicationService.getUserPermissions(
                    noPermissionUser, null, null, null, null, "0", null);

            // Then: 空の権限リストが返されることを検証
            assertNotNull(response, "権限レスポンスが取得できること");
            assertNotNull(response.getRoleList(), "ロールリストが設定されていること");
            // 権限なしユーザーの場合、ロールリストは空またはロール内の権限リストが空
            if (!response.getRoleList().isEmpty()) {
                UserPermissionsResponseV2.RoleInfo roleInfo = response.getRoleList().get(0);
                assertThat(roleInfo.getPermissionList()).isEmpty();
            }

            logger.info("✅ 権限なしユーザーテスト完了: 権限数=0");

        } catch (Exception e) {
            logger.error("権限なしユーザーテストエラー: {}", e.getMessage(), e);
            fail("権限なしユーザーテストに失敗しました: " + e.getMessage());
        }
    }

    /**
     * 新業務要件対応版権限取得テスト（V2レスポンス）
     * 一般ユーザーの兼務情報を含むロールベース権限取得を検証
     */
    @Test
    @Order(5)
    @DisplayName("権限システム_新業務要件対応版一般ユーザー権限取得テスト")
    void testPermissionSystem_新業務要件対応版一般ユーザー権限取得() {
        logger.info("=== 新業務要件対応版一般ユーザー権限取得テスト開始 ===");

        try {
            // Given: テスト用ユーザー情報を準備
            UserInfo testUserInfo = createTestUserInfo("115651", "100001", "12345", "0202");

            // When: 新業務要件対応版でユーザー権限を取得（一般ユーザー）
            UserPermissionsResponseV2 response = permissionApplicationService.getUserPermissions(
                    testUserInfo, "12345", "01", "0202", "G001", "0", BusinessConstants.OPERATION_DOWNLOAD_CODE);

            // Then: 結果を検証
            assertNotNull(response, "権限レスポンスが取得できること");
            assertThat(response.getSystemAdminFlag()).isEqualTo("0"); // 一般ユーザー
            assertNotNull(response.getRoleList(), "ロールリストが設定されていること");

            // 一般ユーザー権限の詳細検証
            verifyGeneralUserPermissions(response);

            logger.info("✅ 新業務要件対応版一般ユーザー権限取得テスト完了: ロール数={}", response.getRoleList().size());

        } catch (Exception e) {
            logger.error("新業務要件対応版一般ユーザー権限取得テストエラー: {}", e.getMessage(), e);
            fail("新業務要件対応版一般ユーザー権限取得テストに失敗しました: " + e.getMessage());
        }
    }

    /**
     * システム管理者権限取得テスト（V2レスポンス）
     * パラメータ提供時のシステム管理者フラグ処理を検証
     */
    @Test
    @Order(6)
    @DisplayName("権限システム_システム管理者権限取得テスト")
    void testPermissionSystem_システム管理者権限取得() {
        logger.info("=== システム管理者権限取得テスト開始 ===");

        try {
            // Given: テスト用ユーザー情報を準備
            UserInfo testUserInfo = createTestUserInfo("115651", "100001", "12345", "0202");

            // When: システム管理者フラグ"1"で権限を取得
            UserPermissionsResponseV2 response = permissionApplicationService.getUserPermissions(
                    testUserInfo, "12345", "01", "0202", "G001", "1", BusinessConstants.OPERATION_DOWNLOAD_CODE);

            // Then: システム管理者権限の検証
            assertNotNull(response, "権限レスポンスが取得できること");
            assertThat(response.getSystemAdminFlag()).isEqualTo("1"); // システム管理者
            assertNotNull(response.getRoleList(), "ロールリストが設定されていること");

            // システム管理者権限の詳細検証
            verifySystemAdminPermissions(response);

            logger.info("✅ システム管理者権限取得テスト完了: ロール数={}", response.getRoleList().size());

        } catch (Exception e) {
            logger.error("システム管理者権限取得テストエラー: {}", e.getMessage(), e);
            fail("システム管理者権限取得テストに失敗しました: " + e.getMessage());
        }
    }

    /**
     * 操作区分別フィルタリングテスト（V2レスポンス）
     * アップロード権限のみの取得を検証
     */
    @Test
    @Order(7)
    @DisplayName("権限システム_V2アップロード権限フィルタリングテスト")
    void testPermissionSystem_V2アップロード権限フィルタリング() {
        logger.info("=== V2アップロード権限フィルタリングテスト開始 ===");

        try {
            // Given: テスト用ユーザー情報を準備
            UserInfo testUserInfo = createTestUserInfo("115651", "100001", "12345", "0202");

            // When: システム管理者でアップロード権限のみを取得
            UserPermissionsResponseV2 response = permissionApplicationService.getUserPermissions(
                    testUserInfo, "12345", "01", "0202", "G001", "1", BusinessConstants.OPERATION_UPLOAD_CODE);

            // Then: アップロード権限のみが返されることを検証
            assertNotNull(response, "権限レスポンスが取得できること");
            assertNotNull(response.getRoleList(), "ロールリストが設定されていること");
            assertThat(response.getRoleList()).isNotEmpty();

            UserPermissionsResponseV2.RoleInfo roleInfo = response.getRoleList().get(0);
            assertNotNull(roleInfo.getPermissionList(), "権限リストが設定されていること");

            // 全ての権限がアップロード権限であることを確認（V2では"1"がアップロード）
            roleInfo.getPermissionList().forEach(permission -> {
                assertThat(permission.getOperationDivision()).isEqualTo("1"); // V2: 1=アップロード
                logger.debug("V2アップロード権限確認: {}", permission.getPermissionCode());
            });

            logger.info("✅ V2アップロード権限フィルタリングテスト完了: {}件", roleInfo.getPermissionList().size());

        } catch (Exception e) {
            logger.error("V2アップロード権限フィルタリングテストエラー: {}", e.getMessage(), e);
            fail("V2アップロード権限フィルタリングテストに失敗しました: " + e.getMessage());
        }
    }

    // ==================== 権限コード解析・重複排除テスト ====================

    // ==================== 権限コード解析・重複排除テスト ====================

    /**
     * 権限コード解析テスト
     * PermissionCodeParserの解析ロジックを検証
     */
    @Test
    @Order(8)
    @DisplayName("権限システム_権限コード解析テスト")
    void testPermissionSystem_権限コード解析() {
        logger.info("=== 権限コード解析テスト開始 ===");

        try {
            // Given: 各種権限コードを準備
            String downloadHeadOfficeCode = "DLH001"; // ダウンロード・本社・計画マスタ
            String uploadAreaCode = "ULA002";         // アップロード・エリア・見通し計画本社
            String invalidCode = "INVALID";           // 無効なコード

            // When & Then: 正常な権限コードの解析を検証
            UserPermissionInfo downloadPermission = PermissionService.parsePermissionCode(downloadHeadOfficeCode);
            assertThat(downloadPermission.getPermissionCode()).isEqualTo(downloadHeadOfficeCode);
            assertThat(downloadPermission.getOperationDivision()).isEqualTo(BusinessConstants.OPERATION_DOWNLOAD_CODE); // "2"
            assertThat(downloadPermission.getAreaPattern()).isEqualTo(BusinessConstants.AFFILIATION_HEAD_OFFICE); // "1"
            assertThat(downloadPermission.getFileType()).isEqualTo(BusinessConstants.FILE_TYPE_PLAN_MASTER_CODE); // "1"

            UserPermissionInfo uploadPermission = PermissionService.parsePermissionCode(uploadAreaCode);
            assertThat(uploadPermission.getPermissionCode()).isEqualTo(uploadAreaCode);
            assertThat(uploadPermission.getOperationDivision()).isEqualTo(BusinessConstants.OPERATION_UPLOAD_CODE); // "1"
            assertThat(uploadPermission.getAreaPattern()).isEqualTo(BusinessConstants.AFFILIATION_AREA); // "2"
            assertThat(uploadPermission.getFileType()).isEqualTo(BusinessConstants.FILE_TYPE_BUDGET_HONSHA_CODE); // "2"

            // 無効なコードの場合の例外処理を検証
            assertThrows(IllegalArgumentException.class, () -> {
                PermissionService.parsePermissionCode(invalidCode);
            });

            logger.info("✅ 権限コード解析テスト完了");

        } catch (Exception e) {
            logger.error("権限コード解析テストエラー: {}", e.getMessage(), e);
            fail("権限コード解析テストに失敗しました: " + e.getMessage());
        }
    }

    /**
     * 権限重複排除テスト
     * 本社権限優先ルールによる重複排除処理を検証
     */
    @Test
    @Order(9)
    @DisplayName("権限システム_権限重複排除テスト")
    void testPermissionSystem_権限重複排除() {
        logger.info("=== 権限重複排除テスト開始 ===");

        try {
            // Given: 重複する権限リストを準備（本社権限とエリア権限が混在）
            List<UserPermissionInfo> duplicatePermissions = List.of(
                createPermissionInfo("DLH001", BusinessConstants.OPERATION_DOWNLOAD_CODE, // "2"
                                   BusinessConstants.AFFILIATION_HEAD_OFFICE, BusinessConstants.FILE_TYPE_PLAN_MASTER_CODE), // "1", "1"
                createPermissionInfo("DLA001", BusinessConstants.OPERATION_DOWNLOAD_CODE, // "2"
                                   BusinessConstants.AFFILIATION_AREA, BusinessConstants.FILE_TYPE_PLAN_MASTER_CODE), // "2", "1"
                createPermissionInfo("ULH002", BusinessConstants.OPERATION_UPLOAD_CODE, // "1"
                                   BusinessConstants.AFFILIATION_HEAD_OFFICE, BusinessConstants.FILE_TYPE_BUDGET_HONSHA_CODE), // "1", "2"
                createPermissionInfo("ULA003", BusinessConstants.OPERATION_UPLOAD_CODE, // "1"
                                   BusinessConstants.AFFILIATION_AREA, BusinessConstants.FILE_TYPE_BUDGET_AREA_CODE) // "2", "3"
            );

            // When: 重複排除処理を実行
            List<UserPermissionInfo> deduplicatedPermissions = PermissionService.deduplicatePermissions(duplicatePermissions);

            // Then: 本社権限が優先され、対応するエリア権限が除外されることを検証
            assertThat(deduplicatedPermissions).hasSize(3); // DLA001が除外される

            // 本社権限が残っていることを確認
            assertThat(deduplicatedPermissions.stream()
                .anyMatch(p -> "DLH001".equals(p.getPermissionCode()))).isTrue();

            // 対応するエリア権限が除外されていることを確認
            assertThat(deduplicatedPermissions.stream()
                .anyMatch(p -> "DLA001".equals(p.getPermissionCode()))).isFalse();

            logger.info("✅ 権限重複排除テスト完了: 重複排除後権限数={}", deduplicatedPermissions.size());

        } catch (Exception e) {
            logger.error("権限重複排除テストエラー: {}", e.getMessage(), e);
            fail("権限重複排除テストに失敗しました: " + e.getMessage());
        }
    }

    // ==================== プライベートヘルパーメソッド ====================

    /**
     * テスト用ユーザー情報を作成
     *
     * @param shainCode 社員コード
     * @param systemOperationCompanyCode システム運用企業コード
     * @param unitCode ユニットコード
     * @param areaCode エリアコード
     * @return テスト用ユーザー情報
     */
    private UserInfo createTestUserInfo(String shainCode, String systemOperationCompanyCode,
                                       String unitCode, String areaCode) {
        UserInfo userInfo = new UserInfo();
        userInfo.setShainCode(shainCode);
        userInfo.setSystemOperationCompanyCode(systemOperationCompanyCode);
        userInfo.setUnitCode(unitCode);
        userInfo.setAreaCode(areaCode);
        return userInfo;
    }

    /**
     * テスト用権限情報を作成
     *
     * @param permissionCode 権限コード
     * @param operationDivision 操作区分
     * @param areaPattern エリアパターン
     * @param fileType ファイル種別
     * @return テスト用権限情報
     */
    private UserPermissionInfo createPermissionInfo(String permissionCode, String operationDivision,
                                                   String areaPattern, String fileType) {
        UserPermissionInfo info = new UserPermissionInfo();
        info.setPermissionCode(permissionCode);
        info.setOperationDivision(operationDivision);
        info.setAreaPattern(areaPattern);
        info.setFileType(fileType);
        return info;
    }

    /**
     * 期待される権限の検証（V2形式）
     * 取得された権限リストが期待される内容を含んでいることを検証
     *
     * @param permissions 検証対象の権限リスト（V2形式）
     */
    private void verifyExpectedPermissionsV2(List<UserPermissionsResponseV2.PermissionInfo> permissions) {
        assertThat(permissions).isNotEmpty();

        // 各権限の基本項目が設定されていることを確認
        for (UserPermissionsResponseV2.PermissionInfo permission : permissions) {
            // 権限コードが設定されていることを確認
            assertThat(permission.getPermissionCode()).isNotNull().isNotEmpty();

            // 操作区分が正しく設定されていることを確認（V2では数値形式）
            assertThat(permission.getOperationDivision())
                    .isIn("1", "2"); // V2: 1=アップロード, 2=ダウンロード

            // エリアパターンが正しく設定されていることを確認（V2では数値形式）
            assertThat(permission.getAreaPattern())
                    .isIn("1", "2"); // V2: 1=本社, 2=エリア

            // ファイル種別コードが設定されていることを確認
            assertThat(permission.getFileType()).isNotNull();

            logger.debug("V2権限検証完了: コード={}, 操作={}, エリア={}, ファイル種別={}",
                    permission.getPermissionCode(),
                    permission.getOperationDivision(),
                    permission.getAreaPattern(),
                    permission.getFileType());
        }
    }

    /**
     * 権限重複排除処理の検証（V2形式）
     * 本社権限優先ルールが正しく適用されていることを検証
     *
     * @param permissions 検証対象の権限リスト（V2形式）
     */
    private void verifyPermissionDeduplicationV2(List<UserPermissionsResponseV2.PermissionInfo> permissions) {
        // 権限コードのペアを確認（本社権限とエリア権限の重複チェック）
        Map<String, List<UserPermissionsResponseV2.PermissionInfo>> groupedByBaseCode = permissions.stream()
                .collect(Collectors.groupingBy(p -> {
                    String code = p.getPermissionCode();
                    // 3桁目を除いた基本コードでグループ化
                    return code.substring(0, 2) + code.substring(3);
                }));

        // 同じ基本コードで本社権限とエリア権限が共存していないことを確認
        groupedByBaseCode.forEach((baseCode, permissionGroup) -> {
            boolean hasHeadOffice = permissionGroup.stream()
                    .anyMatch(p -> p.getPermissionCode().charAt(2) == 'H');
            boolean hasArea = permissionGroup.stream()
                    .anyMatch(p -> p.getPermissionCode().charAt(2) == 'A');

            if (hasHeadOffice && hasArea) {
                fail(String.format("権限重複排除が正しく動作していません。基本コード %s で本社権限とエリア権限が共存しています", baseCode));
            }

            logger.debug("V2権限重複排除検証完了: 基本コード={}, 権限数={}", baseCode, permissionGroup.size());
        });

        logger.info("V2権限重複排除処理が正しく動作していることを確認しました");
    }

    /**
     * システム管理者権限の検証
     * システム管理者フラグと権限内容が正しく設定されていることを検証
     *
     * @param response 検証対象のレスポンス
     */
    private void verifySystemAdminPermissions(UserPermissionsResponseV2 response) {
        assertThat(response.getSystemAdminFlag()).isEqualTo("1");
        assertThat(response.getScreenDisplayFlag()).isEqualTo("0"); // 表示可
        assertNotNull(response.getRoleList());
        assertThat(response.getRoleList()).isNotEmpty();

        // システム管理者の場合、全ての権限が設定されていることを確認
        UserPermissionsResponseV2.RoleInfo roleInfo = response.getRoleList().get(0);
        assertNotNull(roleInfo.getPermissionList());
        assertThat(roleInfo.getPermissionList()).isNotEmpty();

        logger.info("システム管理者権限検証完了: ロール数={}, 権限数={}",
                   response.getRoleList().size(),
                   roleInfo.getPermissionList().size());
    }

    /**
     * 一般ユーザー権限の検証
     * 一般ユーザーフラグと権限内容が正しく設定されていることを検証
     *
     * @param response 検証対象のレスポンス
     */
    private void verifyGeneralUserPermissions(UserPermissionsResponseV2 response) {
        assertThat(response.getSystemAdminFlag()).isEqualTo("0");
        assertNotNull(response.getRoleList());

        // 権限がある場合は表示可、ない場合は表示不可
        if (response.getRoleList().isEmpty() ||
            response.getRoleList().stream().allMatch(role -> role.getPermissionList().isEmpty())) {
            assertThat(response.getScreenDisplayFlag()).isEqualTo("0"); // 権限なしでも表示可（業務要件による）
        } else {
            assertThat(response.getScreenDisplayFlag()).isEqualTo("1"); // 表示可
        }

        logger.info("一般ユーザー権限検証完了: ロール数={}", response.getRoleList().size());
    }
}
