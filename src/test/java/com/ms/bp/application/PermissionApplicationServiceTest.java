package com.ms.bp.application;

import com.ms.bp.domain.master.model.UserBasicInfo;
import com.ms.bp.domain.master.model.ConcurrentJobInfo;
import com.ms.bp.domain.permission.model.Permission;
import com.ms.bp.domain.permission.model.UserPermissionInfo;
import com.ms.bp.domain.user.model.UserInfo;
import com.ms.bp.interfaces.dto.response.UserPermissionsResponseV2;
import com.ms.bp.shared.common.constants.BusinessConstants;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import org.mockito.MockedStatic;
import org.mockito.Mockito;

import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;

/**
 * PermissionApplicationService単体テスト
 * 権限モジュール重構後の機能をテスト
 */
class PermissionApplicationServiceTest {

    private PermissionApplicationService permissionApplicationService;
    private UserInfo testUserInfo;

    @BeforeEach
    void setUp() {
        // テスト用ユーザー情報を初期化
        testUserInfo = new UserInfo();
        testUserInfo.setShainCode("123456");
        testUserInfo.setSystemOperationCompanyCode("000001");
        testUserInfo.setUnitCode("12345");
        testUserInfo.setPositionCode("01");
        testUserInfo.setAreaCode("1000");
        testUserInfo.setAreaName("東京エリア");
        testUserInfo.setGroupCode("G001");

        permissionApplicationService = new PermissionApplicationService();
    }

    @Test
    @DisplayName("パラメータ空の場合の主職務取得テスト")
    void testGetPrimaryJobCombination_WithEmptyParameters() {
        // テスト用ユーザー基本情報を準備
        UserBasicInfo userBasicInfo = new UserBasicInfo();
        userBasicInfo.setUnitCode("12345");
        userBasicInfo.setPositionCode("01");
        userBasicInfo.setAreaCode("1000");
        userBasicInfo.setGroupCode("G001");

        try (MockedStatic<com.ms.bp.shared.util.LambdaResourceManager> mockedLambdaResourceManager = 
             Mockito.mockStatic(com.ms.bp.shared.util.LambdaResourceManager.class)) {
            
            // LambdaResourceManager.executeReadOnlyをモック
            mockedLambdaResourceManager.when(() -> 
                com.ms.bp.shared.util.LambdaResourceManager.executeReadOnly(any()))
                .thenReturn(Optional.of(userBasicInfo));

            // パラメータ化権限取得を実行（パラメータは空）
            UserPermissionsResponseV2 response = permissionApplicationService.getUserPermissions(
                testUserInfo, null, null, null, null, "0", BusinessConstants.OPERATION_DOWNLOAD_CODE);

            // 結果検証
            assertNotNull(response, "レスポンスがnullではないこと");
            assertEquals(testUserInfo.getSystemOperationCompanyCode(), response.getSystemOperationCompanyCode(),
                        "システム運用企業コードが正しく設定されること");
            assertEquals(testUserInfo.getShainCode(), response.getShainCode(),
                        "社員コードが正しく設定されること");
            assertNotNull(response.getRoleList(), "ロールリストがnullではないこと");
        }
    }

    @Test
    @DisplayName("システム管理者権限取得テスト")
    void testGetSystemAdminPermissions() {
        // テスト用本社権限を準備
        Permission headOfficePermission = new Permission();
        headOfficePermission.setPermissionCode("DLH001");
        headOfficePermission.setHantCode("1");
        List<Permission> headOfficePermissions = List.of(headOfficePermission);

        try (MockedStatic<com.ms.bp.shared.util.LambdaResourceManager> mockedLambdaResourceManager = 
             Mockito.mockStatic(com.ms.bp.shared.util.LambdaResourceManager.class)) {
            
            // システム管理者判定をtrueに設定
            mockedLambdaResourceManager.when(() -> 
                com.ms.bp.shared.util.LambdaResourceManager.executeReadOnly(any()))
                .thenReturn(true) // 最初の呼び出し：システム管理者判定
                .thenReturn(headOfficePermissions); // 2回目の呼び出し：本社権限取得

            // システム管理者フラグを"0"に設定（自動判定）
            UserPermissionsResponseV2 response = permissionApplicationService.getUserPermissions(
                testUserInfo, "12345", "01", "1000", "G001", "0", BusinessConstants.OPERATION_DOWNLOAD_CODE);

            // 結果検証
            assertNotNull(response, "レスポンスがnullではないこと");
            assertEquals("1", response.getSystemAdminFlag(), "システム管理者フラグが'1'に設定されること");
            assertEquals("0", response.getScreenDisplayFlag(), "画面表示可否が'0'（表示可）に設定されること");
            assertNotNull(response.getRoleList(), "ロールリストがnullではないこと");
            assertFalse(response.getRoleList().isEmpty(), "ロールリストが空ではないこと");
        }
    }

    @Test
    @DisplayName("兼務情報取得テスト")
    void testGetConcurrentJobsWithAreaInfo() {
        // テスト用兼務情報を準備
        ConcurrentJobInfo concurrentJob = new ConcurrentJobInfo();
        concurrentJob.setUnitCode("54321");
        concurrentJob.setPositionDivision("02");
        concurrentJob.setAreaCode("2000");
        List<ConcurrentJobInfo> concurrentJobs = List.of(concurrentJob);

        // テスト用権限情報を準備
        UserPermissionInfo permission = new UserPermissionInfo();
        permission.setPermissionCode("DLA002");
        permission.setOperationDivision(BusinessConstants.OPERATION_DOWNLOAD_CODE);
        List<UserPermissionInfo> permissions = List.of(permission);

        try (MockedStatic<com.ms.bp.shared.util.LambdaResourceManager> mockedLambdaResourceManager = 
             Mockito.mockStatic(com.ms.bp.shared.util.LambdaResourceManager.class)) {
            
            // LambdaResourceManagerの呼び出しをモック
            mockedLambdaResourceManager.when(() -> 
                com.ms.bp.shared.util.LambdaResourceManager.executeReadOnly(any()))
                .thenReturn(false) // システム管理者判定：false
                .thenReturn(concurrentJobs) // 兼務情報取得
                .thenReturn(permissions) // 主職務権限取得
                .thenReturn(permissions) // 兼務権限取得
                .thenReturn(List.of("12345")) // MS戦略・コーポOF判定
                .thenReturn(List.of()); // エリア情報取得

            // 一般ユーザーの権限取得を実行
            UserPermissionsResponseV2 response = permissionApplicationService.getUserPermissions(
                testUserInfo, "12345", "01", "1000", "G001", "0", BusinessConstants.OPERATION_DOWNLOAD_CODE);

            // 結果検証
            assertNotNull(response, "レスポンスがnullではないこと");
            assertEquals("0", response.getSystemAdminFlag(), "システム管理者フラグが'0'（一般ユーザー）に設定されること");
            assertNotNull(response.getRoleList(), "ロールリストがnullではないこと");
        }
    }

    @Test
    @DisplayName("パラメータ提供済みかつシステム管理者フラグ'1'のテスト")
    void testGetUserPermissions_WithParametersAndSystemAdminFlag() {
        // テスト用本社権限を準備
        Permission headOfficePermission = new Permission();
        headOfficePermission.setPermissionCode("ULH003");
        headOfficePermission.setHantCode("2");
        List<Permission> headOfficePermissions = List.of(headOfficePermission);

        try (MockedStatic<com.ms.bp.shared.util.LambdaResourceManager> mockedLambdaResourceManager = 
             Mockito.mockStatic(com.ms.bp.shared.util.LambdaResourceManager.class)) {
            
            // 本社権限取得をモック
            mockedLambdaResourceManager.when(() -> 
                com.ms.bp.shared.util.LambdaResourceManager.executeReadOnly(any()))
                .thenReturn(headOfficePermissions);

            // パラメータ提供済み＋システム管理者フラグ"1"で実行
            UserPermissionsResponseV2 response = permissionApplicationService.getUserPermissions(
                testUserInfo, "12345", "01", "1000", "G001", "1", BusinessConstants.OPERATION_UPLOAD_CODE);

            // 結果検証
            assertNotNull(response, "レスポンスがnullではないこと");
            assertEquals("1", response.getSystemAdminFlag(), "システム管理者フラグが'1'に設定されること");
            assertEquals("0", response.getScreenDisplayFlag(), "画面表示可否が'0'（表示可）に設定されること");
            assertNotNull(response.getRoleList(), "ロールリストがnullではないこと");
            assertFalse(response.getRoleList().isEmpty(), "ロールリストが空ではないこと");

            // ロール情報の検証
            UserPermissionsResponseV2.RoleInfo roleInfo = response.getRoleList().get(0);
            assertEquals("12345", roleInfo.getUnitCode(), "ユニットコードが正しく設定されること");
            assertEquals("01", roleInfo.getPositionCode(), "役職区分コードが正しく設定されること");
            assertEquals("1000", roleInfo.getAreaCode(), "エリアコードが正しく設定されること");
        }
    }

    @Test
    @DisplayName("操作種別フィルタリングテスト")
    void testOperationTypeFiltering() {
        // アップロード権限とダウンロード権限を含む権限リストを準備
        Permission uploadPermission = new Permission();
        uploadPermission.setPermissionCode("ULH001");
        uploadPermission.setHantCode("1");

        Permission downloadPermission = new Permission();
        downloadPermission.setPermissionCode("DLH001");
        downloadPermission.setHantCode("1");

        List<Permission> allPermissions = List.of(uploadPermission, downloadPermission);

        try (MockedStatic<com.ms.bp.shared.util.LambdaResourceManager> mockedLambdaResourceManager =
             Mockito.mockStatic(com.ms.bp.shared.util.LambdaResourceManager.class)) {

            // 全権限を返すようにモック
            mockedLambdaResourceManager.when(() ->
                com.ms.bp.shared.util.LambdaResourceManager.executeReadOnly(any()))
                .thenReturn(allPermissions);

            // アップロード操作でフィルタリング
            UserPermissionsResponseV2 response = permissionApplicationService.getUserPermissions(
                testUserInfo, "12345", "01", "1000", "G001", "1", BusinessConstants.OPERATION_UPLOAD_CODE);

            // 結果検証：アップロード権限のみが含まれること
            assertNotNull(response.getRoleList(), "ロールリストがnullではないこと");
            assertFalse(response.getRoleList().isEmpty(), "ロールリストが空ではないこと");

            UserPermissionsResponseV2.RoleInfo roleInfo = response.getRoleList().get(0);
            assertNotNull(roleInfo.getPermissionList(), "権限リストがnullではないこと");
            assertTrue(roleInfo.getPermissionList().stream()
                .allMatch(p -> "0".equals(p.getOperationDivision())), // 新業務要件：0=アップロード
                "アップロード権限のみが含まれること");
        }
    }

    @Test
    @DisplayName("新業務要件対応：ユニット名・グループ名取得テスト")
    void testUnitAndGroupNameRetrieval() {
        // テスト用本社権限を準備
        Permission headOfficePermission = new Permission();
        headOfficePermission.setPermissionCode("DLH002");
        headOfficePermission.setHantCode("1");
        List<Permission> headOfficePermissions = List.of(headOfficePermission);

        try (MockedStatic<com.ms.bp.shared.util.LambdaResourceManager> mockedLambdaResourceManager =
             Mockito.mockStatic(com.ms.bp.shared.util.LambdaResourceManager.class)) {

            // 本社権限取得をモック
            mockedLambdaResourceManager.when(() ->
                com.ms.bp.shared.util.LambdaResourceManager.executeReadOnly(any()))
                .thenReturn(headOfficePermissions);

            // システム管理者権限取得を実行
            UserPermissionsResponseV2 response = permissionApplicationService.getUserPermissions(
                testUserInfo, "12345", "01", "1000", "G001", "1", BusinessConstants.OPERATION_DOWNLOAD_CODE);

            // 結果検証：新しいフィールドが設定されること
            assertNotNull(response, "レスポンスがnullではないこと");
            assertNotNull(response.getScreenDisplayFlag(), "画面表示可否が設定されること");
            assertNotNull(response.getSystemAdminFlag(), "システム管理者フラグが設定されること");
            assertNotNull(response.getRoleList(), "ロールリストがnullではないこと");
            assertFalse(response.getRoleList().isEmpty(), "ロールリストが空ではないこと");

            // ロール情報の検証
            UserPermissionsResponseV2.RoleInfo roleInfo = response.getRoleList().get(0);
            assertNotNull(roleInfo.getUnitName(), "ユニット名が設定されること");
            assertNotNull(roleInfo.getGroupName(), "グループ名が設定されること");
            assertNotNull(roleInfo.getPositionName(), "役職区分名が設定されること");

            // 権限コード拆分ロジックの検証
            if (!roleInfo.getPermissionList().isEmpty()) {
                UserPermissionsResponseV2.PermissionInfo permission = roleInfo.getPermissionList().get(0);
                assertEquals("2", permission.getFileType(), "ファイル種別が正しく設定されること"); // 002 -> 2
                assertEquals("1", permission.getOperationDivision(), "操作区分が正しく設定されること"); // DL -> 1
                assertEquals("1", permission.getAreaPattern(), "エリアパターンが正しく設定されること"); // H -> 1
            }
        }
    }
}
